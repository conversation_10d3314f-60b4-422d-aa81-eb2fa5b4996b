"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";

const skills = [
  { name: "HTML", icon: "🌐" },
  { name: "CSS", icon: "🎨" },
  { name: "JavaScript", icon: "⚡" },
  { name: "TypeScript", icon: "📘" },
  { name: "Python", icon: "🐍" },
  { name: "React", icon: "⚛️" },
  { name: "Next.js", icon: "▲" },
  { name: "Tailwind", icon: "💨" },
  { name: "Django", icon: "🎯" },
  { name: "Node.js", icon: "💚" },
  { name: "SQL", icon: "🗄️" },
  { name: "PostgreSQL", icon: "🐘" },
  { name: "MySQL", icon: "🐬" },
  { name: "Docker", icon: "🐳" },
  { name: "Nginx", icon: "🔧" },
  { name: "Git", icon: "📝" },
  { name: "GitH<PERSON>", icon: "🐙" },
  { name: "jQ<PERSON><PERSON>", icon: "💫" },
  { name: "Sass", icon: "💎" },
];

const SkillsSlider = () => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const slider1Ref = useRef<HTMLDivElement>(null);
  const slider2Ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      const slider1 = slider1Ref.current;
      const slider2 = slider2Ref.current;

      if (slider1 && slider2) {
        // خلي الاتنين جنبا بعض
        gsap.set(slider2, { xPercent: 100 });

        // Animation بالـ modifiers → تمنع الـ overlap
        gsap.to([slider1, slider2], {
          xPercent: "-=100",
          duration: 20,
          repeat: -1,
          ease: "none",
          modifiers: {
            xPercent: gsap.utils.wrap(-100, 100),
          },
        });
      }
    }, sliderRef);

    return () => ctx.revert();
  }, []);

  const SkillItem = ({
    skill,
    index,
  }: {
    skill: (typeof skills)[0];
    index: number;
  }) => (
    <div
      className="flex-shrink-0 mx-8 group cursor-pointer"
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      <div className="flex flex-col items-center space-y-3 p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-110 hover:-translate-y-2">
        <div className="text-4xl group-hover:scale-125 transition-transform duration-300">
          {skill.icon}
        </div>
        <span className="text-white font-medium text-sm whitespace-nowrap">
          {skill.name}
        </span>
      </div>
    </div>
  );

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-r from-slate-900 via-indigo-900 to-slate-900"></div>

      {/* Section Header */}
      <div className="container mx-auto px-4 mb-16 relative z-10">
        <div className="text-center">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">
            <span className="bg-gradient-to-r from-indigo-400 to-emerald-400 bg-clip-text text-transparent">
              Skills & Technologies
            </span>
          </h2>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            A comprehensive toolkit for building modern web applications
          </p>
        </div>
      </div>

      {/* Skills Slider */}
      <div ref={sliderRef} className="relative">
        <div className="flex overflow-hidden">
          {/* First slider */}
          <div ref={slider1Ref} className="flex items-center min-w-full">
            {skills.map((skill, index) => (
              <SkillItem key={`first-${index}`} skill={skill} index={index} />
            ))}
          </div>

          {/* Second slider (duplicate for seamless loop) */}
          <div ref={slider2Ref} className="flex items-center min-w-full">
            {skills.map((skill, index) => (
              <SkillItem key={`second-${index}`} skill={skill} index={index} />
            ))}
          </div>
        </div>

        {/* Gradient overlays for smooth edges */}
        <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-slate-900 to-transparent z-10 pointer-events-none"></div>
        <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-slate-900 to-transparent z-10 pointer-events-none"></div>
      </div>

      {/* Decorative elements */}
      <div className="absolute top-1/2 left-10 w-20 h-20 bg-indigo-500/20 rounded-full blur-xl"></div>
      <div className="absolute top-1/4 right-10 w-32 h-32 bg-emerald-500/20 rounded-full blur-xl"></div>
    </section>
  );
};

export default SkillsSlider;
